// player-enhancement.test.ts - Test suite for enhanced player functionality
import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { PlayerComponent } from './player.component';
import { ContentSyncService } from '../../core/services/content-sync.service';
import { PlayerConfigService } from '../../core/services/player-config.service';
import { MediaSchedulerService } from '../../core/services/media-scheduler.service';
import { PlaybackService } from '../../core/services/playback.service';
import { LogService } from '../../core/services/log.service';

describe('Enhanced Player Component', () => {
  let component: PlayerComponent;
  let contentSyncService: jasmine.SpyObj<ContentSyncService>;
  let playerConfigService: jasmine.SpyObj<PlayerConfigService>;
  let mediaSchedulerService: jasmine.SpyObj<MediaSchedulerService>;
  let playbackService: jasmine.SpyObj<PlaybackService>;

  beforeEach(async () => {
    const contentSyncSpy = jasmine.createSpyObj('ContentSyncService', [
      'getLocalContentUrl',
      'downloadScheduledMedia',
      'getBulkDownloadStatus',
      'downloadContentWithRetry'
    ]);

    const configSpy = jasmine.createSpyObj('PlayerConfigService', [
      'getConfig',
      'getCurrentConfig',
      'updateConfig',
      'applyPlaylistSettings',
      'getCarouselConfig',
      'getPlaylistTransitionConfig'
    ]);

    const schedulerSpy = jasmine.createSpyObj('MediaSchedulerService', [
      'getScheduleState',
      'getCurrentSchedule',
      'getNextSchedule',
      'refreshSchedules'
    ]);

    const playbackSpy = jasmine.createSpyObj('PlaybackService', [
      'startPlayback',
      'reloadPlaylist',
      'restartPlayback'
    ]);

    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, PlayerComponent],
      providers: [
        { provide: ContentSyncService, useValue: contentSyncSpy },
        { provide: PlayerConfigService, useValue: configSpy },
        { provide: MediaSchedulerService, useValue: schedulerSpy },
        { provide: PlaybackService, useValue: playbackSpy },
        LogService
      ]
    }).compileComponents();

    contentSyncService = TestBed.inject(ContentSyncService) as jasmine.SpyObj<ContentSyncService>;
    playerConfigService = TestBed.inject(PlayerConfigService) as jasmine.SpyObj<PlayerConfigService>;
    mediaSchedulerService = TestBed.inject(MediaSchedulerService) as jasmine.SpyObj<MediaSchedulerService>;
    playbackService = TestBed.inject(PlaybackService) as jasmine.SpyObj<PlaybackService>;
  });

  describe('Media Download Management', () => {
    it('should handle bulk download with progress tracking', (done) => {
      const mockStatus = {
        totalItems: 5,
        completedItems: 3,
        failedItems: 1,
        currentItem: 'test-item.jpg',
        overallProgress: 60,
        isComplete: false,
        errors: ['Failed to download item1.jpg']
      };

      contentSyncService.getBulkDownloadStatus.and.returnValue(of(mockStatus));

      contentSyncService.getBulkDownloadStatus().subscribe(status => {
        expect(status).toEqual(mockStatus);
        expect(status.overallProgress).toBe(60);
        expect(status.errors.length).toBe(1);
        done();
      });
    });

    it('should retry failed downloads with exponential backoff', (done) => {
      const testUrl = 'https://example.com/test-image.jpg';
      
      // First call fails, second succeeds
      contentSyncService.downloadContentWithRetry.and.returnValues(
        throwError('Network error'),
        of('blob:local-url')
      );

      // Test that retry mechanism works
      contentSyncService.downloadContentWithRetry(testUrl, 2).subscribe({
        next: (localUrl) => {
          expect(localUrl).toBe('blob:local-url');
          expect(contentSyncService.downloadContentWithRetry).toHaveBeenCalledTimes(1);
          done();
        },
        error: () => {
          fail('Should have succeeded on retry');
        }
      });
    });

    it('should filter and download only uncached content', () => {
      const mockPlaylists = [
        {
          id: 'playlist1',
          items: [
            { id: '1', content: { url: 'image1.jpg' } },
            { id: '2', content: { url: 'image2.jpg' } }
          ]
        }
      ];

      const mockDownloadStatus = {
        totalItems: 2,
        completedItems: 0,
        failedItems: 0,
        currentItem: null,
        overallProgress: 0,
        isComplete: false,
        errors: []
      };

      contentSyncService.downloadScheduledMedia.and.returnValue(of(mockDownloadStatus));

      contentSyncService.downloadScheduledMedia(mockPlaylists).subscribe(status => {
        expect(status.totalItems).toBe(2);
        expect(contentSyncService.downloadScheduledMedia).toHaveBeenCalledWith(mockPlaylists);
      });
    });
  });

  describe('Configuration Management', () => {
    it('should load and apply configuration settings', () => {
      const mockConfig = {
        carousel: {
          transitionDuration: 750,
          transitionType: 'fade' as const,
          autoAdvance: true,
          defaultItemDuration: 15,
          preloadNextItems: 3,
          pauseOnError: false,
          errorDisplayDuration: 5
        },
        playlistTransition: {
          enabled: true,
          duration: 1500,
          type: 'crossfade' as const,
          preloadTime: 10
        }
      };

      playerConfigService.getConfig.and.returnValue(of(mockConfig));
      playerConfigService.getCarouselConfig.and.returnValue(mockConfig.carousel);
      playerConfigService.getPlaylistTransitionConfig.and.returnValue(mockConfig.playlistTransition);

      const carouselConfig = playerConfigService.getCarouselConfig();
      expect(carouselConfig.transitionDuration).toBe(750);
      expect(carouselConfig.transitionType).toBe('fade');
      expect(carouselConfig.defaultItemDuration).toBe(15);

      const transitionConfig = playerConfigService.getPlaylistTransitionConfig();
      expect(transitionConfig.duration).toBe(1500);
      expect(transitionConfig.type).toBe('crossfade');
    });

    it('should apply playlist-specific settings', () => {
      const playlistSettings = {
        transition: { type: 'slide', duration: 1000 },
        defaultDuration: 20,
        autoPlay: false
      };

      playerConfigService.applyPlaylistSettings(playlistSettings);
      expect(playerConfigService.applyPlaylistSettings).toHaveBeenCalledWith(playlistSettings);
    });

    it('should validate configuration values', () => {
      const invalidConfig = {
        carousel: {
          transitionDuration: -100, // Invalid negative value
          defaultItemDuration: 0,   // Invalid zero value
          preloadNextItems: -1      // Invalid negative value
        }
      };

      // This would be tested in the actual service
      expect(invalidConfig.carousel.transitionDuration).toBeLessThan(0);
      expect(invalidConfig.carousel.defaultItemDuration).toBe(0);
      expect(invalidConfig.carousel.preloadNextItems).toBeLessThan(0);
    });
  });

  describe('Schedule-Based Playback', () => {
    it('should handle schedule changes with smooth transitions', () => {
      const mockScheduleState = {
        currentSchedule: {
          id: 'schedule1',
          playlistId: 'playlist1',
          playlistName: 'Morning Content',
          startTime: '08:00',
          endTime: '12:00',
          daysOfWeek: ['Monday', 'Tuesday', 'Wednesday'],
          priority: 1,
          isActive: true
        },
        nextSchedule: {
          id: 'schedule2',
          playlistId: 'playlist2',
          playlistName: 'Afternoon Content',
          startTime: '12:00',
          endTime: '17:00',
          daysOfWeek: ['Monday', 'Tuesday', 'Wednesday'],
          priority: 2,
          isActive: true
        },
        currentPlaylistId: 'playlist1',
        nextTransitionTime: new Date('2024-01-01T12:00:00'),
        isTransitioning: false,
        preloadingNextPlaylist: false
      };

      mediaSchedulerService.getScheduleState.and.returnValue(of(mockScheduleState));

      mediaSchedulerService.getScheduleState().subscribe(state => {
        expect(state.currentSchedule?.playlistId).toBe('playlist1');
        expect(state.nextSchedule?.playlistId).toBe('playlist2');
        expect(state.isTransitioning).toBe(false);
      });
    });

    it('should preload next scheduled playlist', () => {
      const mockSchedule = {
        id: 'schedule1',
        playlistId: 'playlist1',
        playlistName: 'Test Playlist',
        startTime: '12:00',
        endTime: '17:00',
        daysOfWeek: ['Monday'],
        priority: 1,
        isActive: true,
        settings: {
          preloadTime: 5
        }
      };

      mediaSchedulerService.getNextSchedule.and.returnValue(mockSchedule);
      
      const nextSchedule = mediaSchedulerService.getNextSchedule();
      expect(nextSchedule?.settings?.preloadTime).toBe(5);
      expect(nextSchedule?.playlistId).toBe('playlist1');
    });
  });

  describe('Carousel Playback Logic', () => {
    beforeEach(() => {
      // Mock DOM elements for carousel testing
      const mockContainer = document.createElement('div');
      mockContainer.style.width = '1920px';
      mockContainer.style.height = '1080px';
      
      spyOn(document, 'querySelector').and.returnValue(mockContainer);
    });

    it('should handle different transition types', () => {
      const transitionTypes = ['slide', 'fade', 'none'];
      
      transitionTypes.forEach(type => {
        const config = {
          transitionType: type as 'slide' | 'fade' | 'none',
          transitionDuration: 500
        };
        
        expect(['slide', 'fade', 'none']).toContain(config.transitionType);
        expect(config.transitionDuration).toBeGreaterThan(0);
      });
    });

    it('should preload upcoming carousel items', () => {
      const mockItems = [
        { id: '1', content: { url: 'item1.jpg' }, isLoaded: false },
        { id: '2', content: { url: 'item2.jpg' }, isLoaded: false },
        { id: '3', content: { url: 'item3.jpg' }, isLoaded: false }
      ];

      contentSyncService.getLocalContentUrl.and.returnValue(of('blob:local-url'));

      // Test preloading logic
      mockItems.forEach(item => {
        contentSyncService.getLocalContentUrl(item.content.url).subscribe(localUrl => {
          expect(localUrl).toBe('blob:local-url');
          item.isLoaded = true;
        });
      });

      expect(mockItems.every(item => item.isLoaded)).toBe(true);
    });

    it('should handle item errors gracefully', () => {
      const mockItem = {
        id: '1',
        playlistItem: { name: 'Test Item', content: { url: 'broken-item.jpg' } },
        hasError: false,
        isLoaded: false
      };

      contentSyncService.getLocalContentUrl.and.returnValue(throwError('Load failed'));

      contentSyncService.getLocalContentUrl(mockItem.playlistItem.content.url).subscribe({
        next: () => {
          fail('Should have failed');
        },
        error: (error) => {
          expect(error).toBe('Load failed');
          mockItem.hasError = true;
          mockItem.isLoaded = true; // Mark as loaded with error
        }
      });

      expect(mockItem.hasError).toBe(true);
      expect(mockItem.isLoaded).toBe(true);
    });

    it('should prevent carousel from getting stuck on one item', (done) => {
      const mockItems = [
        {
          id: '1',
          playlistItem: { name: 'Item 1', duration: 5, content: { url: 'item1.jpg' } },
          isLoaded: true,
          isContentReady: true,
          hasError: false
        },
        {
          id: '2',
          playlistItem: { name: 'Item 2', duration: 5, content: { url: 'item2.jpg' } },
          isLoaded: true,
          isContentReady: true,
          hasError: false
        },
        {
          id: '3',
          playlistItem: { name: 'Item 3', duration: 5, content: { url: 'item3.jpg' } },
          isLoaded: true,
          isContentReady: true,
          hasError: false
        }
      ];

      // Test that slideToNext properly cycles through all items
      let currentIndex = 0;
      const slideToNext = () => {
        currentIndex = (currentIndex + 1) % mockItems.length;
        return currentIndex;
      };

      // Simulate cycling through all items
      const firstCycle = [slideToNext(), slideToNext(), slideToNext()];
      expect(firstCycle).toEqual([1, 2, 0]); // Should cycle back to 0

      // Simulate another cycle to ensure it continues
      const secondCycle = [slideToNext(), slideToNext(), slideToNext()];
      expect(secondCycle).toEqual([1, 2, 0]); // Should cycle back to 0 again

      done();
    });

    it('should handle safety timer to prevent getting stuck', (done) => {
      const mockConfig = {
        autoAdvance: true,
        defaultItemDuration: 5 // 5 seconds
      };

      // Simulate safety timer (3x normal duration = 15 seconds)
      const safetyDuration = mockConfig.defaultItemDuration * 3;
      expect(safetyDuration).toBe(15);

      // Test that safety timer would trigger after expected time
      setTimeout(() => {
        // Safety timer should have triggered by now
        expect(true).toBe(true); // Placeholder for actual safety timer logic
        done();
      }, 100); // Short timeout for test
    });
  });

  describe('Integration Tests', () => {
    it('should integrate all services for complete playback flow', (done) => {
      // Mock complete flow
      const mockConfig = {
        carousel: { autoAdvance: true, defaultItemDuration: 10 },
        playlistTransition: { enabled: true, duration: 1000 }
      };

      const mockScheduleState = {
        currentPlaylistId: 'playlist1',
        isTransitioning: false
      };

      const mockDownloadStatus = {
        isComplete: true,
        totalItems: 3,
        completedItems: 3,
        failedItems: 0
      };

      playerConfigService.getConfig.and.returnValue(of(mockConfig));
      mediaSchedulerService.getScheduleState.and.returnValue(of(mockScheduleState));
      contentSyncService.getBulkDownloadStatus.and.returnValue(of(mockDownloadStatus));

      // Test integration
      Promise.all([
        playerConfigService.getConfig().toPromise(),
        mediaSchedulerService.getScheduleState().toPromise(),
        contentSyncService.getBulkDownloadStatus().toPromise()
      ]).then(([config, scheduleState, downloadStatus]) => {
        expect(config.carousel.autoAdvance).toBe(true);
        expect(scheduleState.currentPlaylistId).toBe('playlist1');
        expect(downloadStatus.isComplete).toBe(true);
        done();
      });
    });
  });
});
