// player.component.ts - Carousel-based Digital Signage Player
import { Component, OnInit, OnDestroy, ElementRef, Inject, NgZone, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { PLATFORM_ID } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, Subscription, BehaviorSubject } from 'rxjs';

import { PlaybackService } from '../../core/services/playback.service';
import { ScheduleService } from '../../core/services/schedule.service';
import { HeartbeatService } from '../../core/services/heartbeat.service';
import { LogService } from '../../core/services/log.service';
import { SupabaseApiService } from '../../core/services/supabase-api.service';
import { WakeLockService } from '../../core/services/wake-lock.service';
import { ContentSyncService } from '../../core/services/content-sync.service';
import { MediaSchedulerService } from '../../core/services/media-scheduler.service';
import { PlayerConfigService, CarouselConfig, PlaylistTransitionConfig } from '../../core/services/player-config.service';
import { PlaylistItem } from '../../core/models/playlist.model';
import { PlayerState } from '../../core/models/player-state.model';

// Interface for bulk download status (matching ContentSyncService)
interface BulkDownloadStatus {
  totalItems: number;
  completedItems: number;
  failedItems: number;
  currentItem: string | null;
  overallProgress: number; // 0-100
  isComplete: boolean;
  errors: string[];
}

interface CarouselItem {
  id: string;
  playlistItem: PlaylistItem;
  localContentUrl: string | null;
  isLoaded: boolean;
  hasError: boolean;
  preloadStarted?: boolean;
  loadStartTime?: number;
  isContentReady?: boolean; // New: indicates if content is fully ready for display
  preloadElement?: HTMLImageElement | HTMLVideoElement; // New: preload element for better buffering
}

@Component({
  selector: 'app-player',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './player.component.html',
  styleUrls: ['./player.component.scss']
})
export class PlayerComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('carouselContainer', { static: false }) carouselContainer: ElementRef<HTMLDivElement> | null = null;

  // Carousel state
  carouselItems: CarouselItem[] = [];
  currentIndex = 0;
  isTransitioning = false;

  // Enhanced carousel configuration
  carouselConfig: CarouselConfig = {
    transitionDuration: 500, // 0.5 seconds
    transitionType: 'fade', // Changed to fade for smooth fade transitions
    autoAdvance: true,
    defaultItemDuration: 10, // 10 seconds default
    preloadNextItems: 4, // preload 4 items ahead for better buffering
    pauseOnError: false,
    errorDisplayDuration: 3 // 3 seconds
  };

  playlistTransitionConfig: PlaylistTransitionConfig = {
    enabled: true,
    duration: 1000, // 1 second
    type: 'crossfade',
    preloadTime: 5 // 5 minutes
  };

  // Preloading state
  private preloadQueue: Set<string> = new Set();
  private isPreloading = false;

  // Transition queue for smooth sequencing
  private transitionQueue: number[] = [];
  private isProcessingTransitionQueue = false;

  // Player state
  playerState$: Observable<PlayerState>;
  currentPlayerState: PlayerState | null = null;
  playbackError: string | null = null;
  isFullscreen = false;
  isOnline = typeof window !== 'undefined' ? navigator.onLine : false;
  deviceValidationError: boolean = false;
  private isBrowser: boolean;

  // Timers and intervals
  private lastTimeCheck: number = 0;
  private preciseMinuteInterval: any = null;
  private transitionTimeoutIds: any[] = [];
  private forceReloadTimeoutId: any = null;
  private itemTimer: any = null;
  private safetyTimer: any = null; // Safety timer to prevent carousel from getting stuck

  // Subscriptions
  private subscriptions: Subscription[] = [];
  private heartbeatInterval: any = null;
  private scheduleCheckInterval: Subscription | null = null;

  constructor(
    private playbackService: PlaybackService,
    private scheduleService: ScheduleService,
    private heartbeatService: HeartbeatService,
    private logService: LogService,
    private supabaseApi: SupabaseApiService,
    private wakeLockService: WakeLockService,
    private contentSyncService: ContentSyncService,
    private playerConfigService: PlayerConfigService,
    private router: Router,
    private elementRef: ElementRef,
    private zone: NgZone,
    @Inject(PLATFORM_ID) private platformId: any
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.playerState$ = this.playbackService.playerState$;

    // Initialize configuration
    this.initializeConfiguration();

    this.logService.setDebugLevel(0);
    this.logService.info('Carousel Player component initialized with enhanced configuration');
  }

  /**
   * Initialize configuration from service
   */
  private initializeConfiguration(): void {
    // Subscribe to configuration changes
    const configSub = this.playerConfigService.getConfig().subscribe(config => {
      this.carouselConfig = config.carousel;
      this.playlistTransitionConfig = config.playlistTransition;
      this.logService.debug('Configuration updated', config);
    });

    this.subscriptions.push(configSub);
  }

  ngOnInit(): void {
    if (!this.isBrowser) {
      this.logService.warn('Not running in browser, skipping player initialization');
      return;
    }

    this.validateDevice().then(isValid => {
      if (!isValid) {
        this.deviceValidationError = true;
        this.logService.error('Device validation failed');
        return;
      }

      this.setupNetworkListeners();
      this.setupTimeChangeListeners();
      this.setupPlayback();
      this.setupScheduleChecking();
      this.setupPeriodicDeviceValidation();
      this.startHeartbeat();

      this.enterFullscreen();
      this.requestWakeLock();

      this.logService.info('Enhanced Carousel Player started with configuration management');

      const scheduleChangeSub = this.scheduleService.scheduleChange$.subscribe(playlistId => {
        this.logService.info(`Schedule change received in carousel player: ${playlistId}`);
        this.forceReloadPlaylist(playlistId);
      });

      this.subscriptions.push(scheduleChangeSub);
    });
  }

  ngAfterViewInit(): void {
    // Initialize carousel after view is ready
    if (this.isBrowser && this.carouselContainer) {
      this.logService.info('Carousel container ready');
    }
  }

  ngOnDestroy(): void {
    this.logService.info('Carousel Player component destroyed');

    this.clearAllTimers();
    this.subscriptions.forEach(sub => sub.unsubscribe());

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.scheduleCheckInterval) {
      this.scheduleCheckInterval.unsubscribe();
    }

    this.releaseWakeLock();
  }

  // Carousel navigation methods
  slideToNext(): void {
    if (this.carouselItems.length === 0) {
      this.logService.warn('Cannot slide to next - no carousel items available');
      return;
    }

    if (this.carouselItems.length === 1) {
      this.logService.debug('Only one item in carousel, restarting timer');
      this.startItemTimer();
      return;
    }

    if (this.isTransitioning) {
      this.logService.debug('Already transitioning, queueing next slide');
      const nextIndex = (this.currentIndex + 1) % this.carouselItems.length;
      this.queueTransition(nextIndex);
      return;
    }

    const nextIndex = (this.currentIndex + 1) % this.carouselItems.length;
    this.logService.debug(`Sliding to next item: ${nextIndex + 1}/${this.carouselItems.length}`);
    this.slideTo(nextIndex);
  }

  slideToPrevious(): void {
    if (this.isTransitioning || this.carouselItems.length === 0) return;

    const prevIndex = this.currentIndex === 0 ? this.carouselItems.length - 1 : this.currentIndex - 1;
    this.slideTo(prevIndex);
  }

  slideTo(index: number): void {
    if (index === this.currentIndex || !this.carouselContainer) return;

    // If already transitioning, queue the transition
    if (this.isTransitioning) {
      this.queueTransition(index);
      return;
    }

    // Ensure content readiness is properly checked
    this.ensureContentReadiness(index);

    // Check if next content is ready for smooth transition
    const nextItem = this.carouselItems[index];
    if (!nextItem?.isContentReady && this.carouselConfig.transitionType === 'crossfade') {
      this.logService.debug(`Delaying transition to ${index} - content not ready`);
      // Queue the transition and try to prepare content
      this.queueTransition(index);
      this.preloadUpcomingItems(index);

      // Add fallback timer to force transition if content doesn't become ready
      setTimeout(() => {
        if (this.currentIndex !== index && !this.isTransitioning) {
          this.logService.warn(`Forcing delayed transition to ${index} after timeout`);
          this.executeTransition(index);
        }
      }, 5000); // 5 second fallback
      return;
    }

    this.executeTransition(index);
  }

  /**
   * Ensure content readiness is properly checked for loaded items
   */
  private ensureContentReadiness(index: number): void {
    const item = this.carouselItems[index];
    if (!item) return;

    // If item has error, mark as ready to prevent blocking
    if (item.hasError && !item.isContentReady) {
      item.isContentReady = true;
      this.logService.debug(`Marked error item as ready to prevent blocking: ${item.playlistItem.name}`);
      return;
    }

    // If item is not loaded, try to mark it ready based on type
    if (!item.isLoaded) {
      const contentType = item.playlistItem.type;
      if (contentType === 'ticker' || contentType === 'webpage') {
        // These types don't need preloading
        item.isLoaded = true;
        item.isContentReady = true;
        this.logService.debug(`Marked non-preloadable content as ready: ${item.playlistItem.name}`);
      }
      return;
    }

    // If item is loaded but not marked as content ready, check if we can mark it ready
    if (item.isLoaded && !item.isContentReady && item.localContentUrl) {
      const contentType = item.playlistItem.type;

      if (contentType === 'image') {
        // For images, if loaded, they should be ready
        item.isContentReady = true;
        this.logService.debug(`Marked loaded image as ready: ${item.playlistItem.name}`);
      } else if (contentType === 'video') {
        // For videos, create preload element if not exists
        if (!item.preloadElement) {
          this.createPreloadElement(item, true);
        }
        // If preload element exists and has some data, mark as ready
        if (item.preloadElement && (item.preloadElement as HTMLVideoElement).readyState >= 2) {
          item.isContentReady = true;
          this.logService.debug(`Marked video with loaded data as ready: ${item.playlistItem.name}`);
        }
      } else {
        // For other content types, mark as ready
        item.isContentReady = true;
        this.logService.debug(`Marked other content type as ready: ${item.playlistItem.name}`);
      }
    }
  }

  /**
   * Queue a transition for later execution
   */
  private queueTransition(index: number): void {
    if (!this.transitionQueue.includes(index)) {
      this.transitionQueue.push(index);
      this.logService.debug(`Queued transition to index ${index}`);
    }

    // Process queue if not already processing
    if (!this.isProcessingTransitionQueue) {
      this.processTransitionQueue();
    }
  }

  /**
   * Process the transition queue
   */
  private processTransitionQueue(): void {
    if (this.isProcessingTransitionQueue || this.transitionQueue.length === 0) return;

    this.isProcessingTransitionQueue = true;

    const processNext = () => {
      if (this.transitionQueue.length === 0) {
        this.isProcessingTransitionQueue = false;
        return;
      }

      // If we're already transitioning, wait and try again
      if (this.isTransitioning) {
        setTimeout(processNext, 100);
        return;
      }

      const nextIndex = this.transitionQueue.shift()!;
      const nextItem = this.carouselItems[nextIndex];

      // Ensure content readiness is checked
      this.ensureContentReadiness(nextIndex);

      // Check if content is ready or if we should force transition
      const shouldForceTransition = this.carouselConfig.transitionType !== 'crossfade' ||
                                   !nextItem ||
                                   nextItem.isContentReady ||
                                   nextItem.hasError; // Force transition on error to prevent getting stuck

      if (shouldForceTransition) {
        this.executeTransition(nextIndex);
        // Wait for transition to complete before processing next
        setTimeout(processNext, this.carouselConfig.transitionDuration + 100);
      } else {
        // Content not ready, wait a bit and try again (max 3 retries)
        const retryCount = (nextItem as any)._retryCount || 0;
        if (retryCount < 3) {
          (nextItem as any)._retryCount = retryCount + 1;
          this.transitionQueue.unshift(nextIndex); // Put it back at the front
          this.logService.debug(`Retrying transition to ${nextIndex}, attempt ${retryCount + 1}`);
          setTimeout(processNext, 500); // Slightly longer wait
        } else {
          // Force transition after max retries to prevent getting stuck
          this.logService.warn(`Forcing transition to ${nextIndex} after max retries`);
          this.executeTransition(nextIndex);
          setTimeout(processNext, this.carouselConfig.transitionDuration + 100);
        }
      }
    };

    processNext();
  }

  /**
   * Execute the actual transition
   */
  private executeTransition(index: number): void {
    if (!this.carouselContainer) return;

    this.isTransitioning = true;
    this.clearItemTimer();

    // Pause any videos in the current item
    this.pauseCurrentVideo();

    // Start preloading next items
    this.preloadUpcomingItems(index);

    const container = this.carouselContainer.nativeElement;

    // Apply transition based on configuration
    this.applyCarouselTransition(container, index);

    // Update current index after transition
    setTimeout(() => {
      this.currentIndex = index;
      this.isTransitioning = false;

      // Play video if the new item is a video
      this.playCurrentVideo();

      // Always restart the timer to ensure continuous cycling
      this.startItemTimer();
      this.logService.info(`Carousel slid to item ${index + 1}/${this.carouselItems.length}`);

      // Safety mechanism: if we have multiple items and timer doesn't start, force next transition
      if (this.carouselItems.length > 1 && !this.itemTimer) {
        this.logService.warn('Timer failed to start, forcing next transition in 15 seconds');
        setTimeout(() => {
          if (!this.itemTimer && !this.isTransitioning) {
            this.slideToNext();
          }
        }, 15000);
      }
    }, this.carouselConfig.transitionDuration);
  }

  /**
   * Apply carousel transition based on configuration
   */
  private applyCarouselTransition(container: HTMLElement, targetIndex: number): void {
    const transitionDuration = this.carouselConfig.transitionDuration / 1000; // Convert to seconds

    switch (this.carouselConfig.transitionType) {
      case 'slide':
        const slideWidth = container.offsetWidth;
        const translateX = -targetIndex * slideWidth;
        container.style.transition = `transform ${transitionDuration}s ease-in-out`;
        container.style.transform = `translateX(${translateX}px)`;
        break;

      case 'crossfade':
        this.applyCrossfadeTransition(container, targetIndex, transitionDuration);
        break;

      case 'fade':
        // For fade transition, we'll handle it differently in CSS
        container.style.transition = `opacity ${transitionDuration}s ease-in-out`;
        container.style.opacity = '0';

        setTimeout(() => {
          const slideWidth = container.offsetWidth;
          const translateX = -targetIndex * slideWidth;
          container.style.transition = 'none';
          container.style.transform = `translateX(${translateX}px)`;
          container.style.opacity = '1';
          container.style.transition = `opacity ${transitionDuration}s ease-in-out`;
        }, this.carouselConfig.transitionDuration / 2);
        break;

      case 'none':
        const immediateSlideWidth = container.offsetWidth;
        const immediateTranslateX = -targetIndex * immediateSlideWidth;
        container.style.transition = 'none';
        container.style.transform = `translateX(${immediateTranslateX}px)`;
        break;
    }
  }

  /**
   * Apply crossfade transition for smooth content switching
   */
  private applyCrossfadeTransition(container: HTMLElement, targetIndex: number, transitionDuration: number): void {
    const currentItem = container.children[this.currentIndex] as HTMLElement;
    const targetItem = container.children[targetIndex] as HTMLElement;

    if (!currentItem || !targetItem) return;

    // Position target item at the correct location but invisible
    const slideWidth = container.offsetWidth;
    const targetTranslateX = -targetIndex * slideWidth;

    // Set up initial states
    targetItem.style.opacity = '0';
    targetItem.style.zIndex = '3';
    currentItem.style.zIndex = '2';

    // Position the container to show the target item
    container.style.transition = 'none';
    container.style.transform = `translateX(${targetTranslateX}px)`;

    // Force reflow
    container.offsetHeight;

    // Start crossfade
    targetItem.style.transition = `opacity ${transitionDuration}s ease-in-out`;
    currentItem.style.transition = `opacity ${transitionDuration}s ease-in-out`;

    targetItem.style.opacity = '1';
    currentItem.style.opacity = '0';

    // Clean up after transition
    setTimeout(() => {
      currentItem.style.opacity = '1';
      currentItem.style.zIndex = '1';
      targetItem.style.zIndex = '2';
      currentItem.style.transition = '';
      targetItem.style.transition = '';
    }, this.carouselConfig.transitionDuration);
  }

  /**
   * Preload upcoming carousel items with enhanced readiness checks (bidirectional)
   */
  private preloadUpcomingItems(currentIndex: number): void {
    if (this.isPreloading) return;

    this.isPreloading = true;
    const itemsToPreload = this.carouselConfig.preloadNextItems;

    // Always ensure the next AND previous items are ready for smooth navigation
    const nextIndex = (currentIndex + 1) % this.carouselItems.length;
    const prevIndex = currentIndex === 0 ? this.carouselItems.length - 1 : currentIndex - 1;

    const nextItem = this.carouselItems[nextIndex];
    const prevItem = this.carouselItems[prevIndex];

    // High priority: next and previous items
    if (nextItem && !nextItem.isContentReady) {
      this.preloadCarouselItemWithReadiness(nextItem, true);
    }
    if (prevItem && !prevItem.isContentReady) {
      this.preloadCarouselItemWithReadiness(prevItem, true);
    }

    // Preload additional items in both directions
    for (let i = 1; i <= Math.floor(itemsToPreload / 2); i++) {
      // Forward direction
      const forwardIndex = (currentIndex + i + 1) % this.carouselItems.length;
      const forwardItem = this.carouselItems[forwardIndex];
      if (forwardItem && !forwardItem.isLoaded && !forwardItem.preloadStarted) {
        this.preloadCarouselItemWithReadiness(forwardItem, i <= 1);
      }

      // Backward direction
      const backwardIndex = (currentIndex - i - 1 + this.carouselItems.length) % this.carouselItems.length;
      const backwardItem = this.carouselItems[backwardIndex];
      if (backwardItem && !backwardItem.isLoaded && !backwardItem.preloadStarted) {
        this.preloadCarouselItemWithReadiness(backwardItem, i <= 1);
      }
    }

    this.isPreloading = false;
  }

  /**
   * Preload a specific carousel item with enhanced readiness checks
   */
  private preloadCarouselItemWithReadiness(item: CarouselItem, isHighPriority: boolean = false): void {
    if (item.preloadStarted) return;

    item.preloadStarted = true;
    item.loadStartTime = Date.now();
    item.isContentReady = false;

    this.contentSyncService.getLocalContentUrl(item.playlistItem.content.url).subscribe({
      next: (localUrl) => {
        item.localContentUrl = localUrl;
        item.isLoaded = true;

        // Create preload element for better buffering
        this.createPreloadElement(item, isHighPriority);

        const loadTime = Date.now() - (item.loadStartTime || 0);
        this.logService.debug(`Preloaded item ${item.playlistItem.name} in ${loadTime}ms (priority: ${isHighPriority})`);
      },
      error: (error) => {
        this.logService.error(`Error preloading item: ${error.message}`);
        item.localContentUrl = item.playlistItem.content.url; // Fallback to original URL
        item.hasError = true;
        item.isLoaded = true;
        item.isContentReady = true; // Mark as ready even with error to prevent blocking
      }
    });
  }

  /**
   * Create preload element for better content buffering
   */
  private createPreloadElement(item: CarouselItem, isHighPriority: boolean): void {
    if (!item.localContentUrl) return;

    const contentType = item.playlistItem.type;

    if (contentType === 'image') {
      const img = new Image();
      img.onload = () => {
        item.isContentReady = true;
        this.logService.debug(`Image ready for display: ${item.playlistItem.name}`);
      };
      img.onerror = () => {
        item.hasError = true;
        item.isContentReady = true;
        this.logService.error(`Image preload error: ${item.playlistItem.name}`);
      };
      img.src = item.localContentUrl;
      item.preloadElement = img;

    } else if (contentType === 'video') {
      const video = document.createElement('video');
      video.preload = isHighPriority ? 'auto' : 'metadata';
      video.muted = true; // Required for autoplay

      // Multiple events to ensure readiness
      const markReady = () => {
        if (!item.isContentReady) {
          item.isContentReady = true;
          this.logService.debug(`Video ready for display: ${item.playlistItem.name}`);
        }
      };

      video.oncanplaythrough = markReady;
      video.onloadeddata = () => {
        // For high priority items, wait for canplaythrough
        // For others, loadeddata is sufficient
        if (!isHighPriority) {
          markReady();
        }
      };
      video.onerror = () => {
        item.hasError = true;
        item.isContentReady = true;
        this.logService.error(`Video preload error: ${item.playlistItem.name}`);
      };

      video.src = item.localContentUrl;
      item.preloadElement = video;

    } else {
      // For other content types, mark as ready immediately
      item.isContentReady = true;
    }
  }

  private pauseCurrentVideo(): void {
    const currentItem = this.currentItem;
    if (currentItem?.playlistItem.type === 'video') {
      const videoElements = document.querySelectorAll('.carousel-item.active video');
      videoElements.forEach((video: any) => {
        if (video.pause) {
          video.pause();
        }
      });
    }
  }

  private playCurrentVideo(): void {
    const currentItem = this.currentItem;
    if (currentItem?.playlistItem.type === 'video') {
      setTimeout(() => {
        const videoElements = document.querySelectorAll('.carousel-item.active video');
        videoElements.forEach((video: any) => {
          if (video.play) {
            video.play().catch((error: any) => {
              this.logService.warn(`Video autoplay failed: ${error}`);
            });
          }
        });
      }, 100); // Small delay to ensure DOM is updated
    }
  }

  // Handle video errors
  onVideoError(carouselItem: CarouselItem): void {
    this.logService.error(`Video error for item: ${carouselItem.playlistItem.name}`);
    carouselItem.hasError = true;

    this.handleItemError(carouselItem);
  }

  // Handle image errors
  onImageError(carouselItem: CarouselItem): void {
    this.logService.error(`Image error for item: ${carouselItem.playlistItem.name}`);
    carouselItem.hasError = true;

    this.handleItemError(carouselItem);
  }

  /**
   * Handle errors for carousel items with configurable behavior
   */
  private handleItemError(carouselItem: CarouselItem): void {
    if (this.carouselConfig.pauseOnError) {
      // Pause playback and show error
      this.clearItemTimer();
      this.logService.warn(`Playback paused due to error in item: ${carouselItem.playlistItem.name}`);
      return;
    }

    // Continue to next item after configured delay
    const errorDelay = this.carouselConfig.errorDisplayDuration * 1000;

    setTimeout(() => {
      if (this.carouselItems.length > 1) {
        this.slideToNext();
      } else {
        // If this is the only item, try to reload it
        this.reloadCurrentItem();
      }
    }, errorDelay);
  }

  /**
   * Reload the current carousel item
   */
  private reloadCurrentItem(): void {
    const currentItem = this.currentItem;
    if (!currentItem) return;

    this.logService.info(`Reloading current item: ${currentItem.playlistItem.name}`);

    currentItem.hasError = false;
    currentItem.isLoaded = false;
    currentItem.preloadStarted = false;
    currentItem.localContentUrl = null;

    // Reload the content
    this.contentSyncService.getLocalContentUrl(currentItem.playlistItem.content.url).subscribe({
      next: (localUrl) => {
        currentItem.localContentUrl = localUrl;
        currentItem.isLoaded = true;
        this.logService.info(`Successfully reloaded item: ${currentItem.playlistItem.name}`);

        // Restart the timer
        this.startItemTimer();
      },
      error: (error) => {
        this.logService.error(`Failed to reload item: ${error.message}`);
        currentItem.localContentUrl = currentItem.playlistItem.content.url; // Fallback
        currentItem.hasError = true;
        currentItem.isLoaded = true;

        // Try again after a longer delay
        setTimeout(() => this.reloadCurrentItem(), 10000);
      }
    });
  }

  // Handle image load success
  onImageLoad(carouselItem: CarouselItem): void {
    carouselItem.isContentReady = true;
    this.logService.debug(`Image loaded successfully: ${carouselItem.playlistItem.name}`);
  }

  // Handle video events for better buffering
  onVideoCanPlayThrough(carouselItem: CarouselItem): void {
    carouselItem.isContentReady = true;
    this.logService.debug(`Video can play through: ${carouselItem.playlistItem.name}`);
  }

  onVideoLoadedData(carouselItem: CarouselItem): void {
    this.logService.debug(`Video data loaded: ${carouselItem.playlistItem.name}`);
  }

  // Get video preload strategy based on position
  getVideoPreloadStrategy(index: number): string {
    const distance = Math.abs(index - this.currentIndex);

    if (index === this.currentIndex) {
      return 'auto'; // Current video should be fully loaded
    } else if (distance <= 1) {
      return 'auto'; // Next/previous videos should be fully loaded
    } else if (distance <= 2) {
      return 'metadata'; // Videos 2 positions away load metadata only
    } else {
      return 'none'; // Distant videos don't preload
    }
  }

  // Get appropriate loading message based on item state
  getLoadingMessage(carouselItem: CarouselItem): string {
    if (!carouselItem.isLoaded) {
      return 'Loading content...';
    } else if (!carouselItem.isContentReady) {
      return carouselItem.playlistItem.type === 'video' ? 'Buffering video...' : 'Preparing content...';
    }
    return 'Loading content...';
  }

  // Get current carousel item
  get currentItem(): CarouselItem | null {
    return this.carouselItems[this.currentIndex] || null;
  }

  // Start timer for current item duration
  private startItemTimer(): void {
    this.clearItemTimer();

    const currentItem = this.currentItem;
    if (!currentItem || !this.carouselConfig.autoAdvance) return;

    // Use item-specific duration or playlist default or global default
    let duration = currentItem.playlistItem.duration;
    if (!duration || duration <= 0) {
      duration = this.carouselConfig.defaultItemDuration;
    }

    const durationMs = duration * 1000; // Convert to milliseconds

    this.logService.debug(`Starting item timer for ${duration}s: ${currentItem.playlistItem.name}`);

    this.itemTimer = setTimeout(() => {
      if (this.carouselConfig.autoAdvance && this.carouselItems.length > 0) {
        this.logService.debug(`Timer expired, advancing from item ${this.currentIndex + 1}/${this.carouselItems.length}`);
        this.slideToNext();
      }
    }, durationMs);

    // Start safety timer to ensure carousel never gets completely stuck
    this.startSafetyTimer();
  }

  /**
   * Start safety timer to prevent carousel from getting stuck
   */
  private startSafetyTimer(): void {
    this.clearSafetyTimer();

    // Only start safety timer if we have multiple items
    if (this.carouselItems.length <= 1) return;

    // Safety timer is 3x the normal duration to catch stuck states
    const currentItem = this.currentItem;
    let duration = currentItem?.playlistItem.duration || this.carouselConfig.defaultItemDuration;
    const safetyDurationMs = duration * 3000; // 3x normal duration

    this.safetyTimer = setTimeout(() => {
      if (this.carouselItems.length > 1 && !this.isTransitioning) {
        this.logService.warn(`Safety timer triggered - carousel may be stuck, forcing next slide`);
        this.slideToNext();
      }
    }, safetyDurationMs);
  }

  private clearSafetyTimer(): void {
    if (this.safetyTimer) {
      clearTimeout(this.safetyTimer);
      this.safetyTimer = null;
    }
  }

  /**
   * Apply playlist-specific configuration settings
   */
  private applyPlaylistConfiguration(playlist: any): void {
    if (playlist?.settings) {
      this.playerConfigService.applyPlaylistSettings(playlist.settings);
      this.logService.info('Applied playlist-specific configuration', playlist.settings);
    }
  }

  /**
   * Get current carousel configuration
   */
  getCarouselConfig(): CarouselConfig {
    return this.playerConfigService.getCarouselConfig();
  }

  /**
   * Get current playlist transition configuration
   */
  getPlaylistTransitionConfig(): PlaylistTransitionConfig {
    return this.playerConfigService.getPlaylistTransitionConfig();
  }

  /**
   * Update configuration dynamically
   */
  updatePlayerConfiguration(updates: any): void {
    this.playerConfigService.updateConfig(updates);
  }

  private clearItemTimer(): void {
    if (this.itemTimer) {
      clearTimeout(this.itemTimer);
      this.itemTimer = null;
    }
    this.clearSafetyTimer();
  }

  private clearAllTimers(): void {
    this.clearItemTimer();
    this.clearSafetyTimer();

    if (this.preciseMinuteInterval) {
      clearInterval(this.preciseMinuteInterval);
      this.preciseMinuteInterval = null;
    }

    this.transitionTimeoutIds.forEach(id => clearTimeout(id));
    this.transitionTimeoutIds = [];

    if (this.forceReloadTimeoutId) {
      clearTimeout(this.forceReloadTimeoutId);
      this.forceReloadTimeoutId = null;
    }
  }

  // Device validation
  private async validateDevice(): Promise<boolean> {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return false;
    }

    const deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      this.logService.error('No device ID found in localStorage');
      return false;
    }

    return new Promise((resolve) => {
      this.supabaseApi.getScreenById(deviceId).subscribe({
        next: (screen) => {
          resolve(!!screen);
        },
        error: (error) => {
          this.logService.error(`Device validation failed: ${error}`);
          resolve(false);
        }
      });
    });
  }

  // Setup methods
  private setupNetworkListeners(): void {
    if (!this.isBrowser) return;

    window.addEventListener('online', () => {
      this.zone.run(() => {
        this.isOnline = true;
        this.logService.info('Network connection restored');
        this.playbackService.startPlayback();
      });
    });

    window.addEventListener('offline', () => {
      this.zone.run(() => {
        this.isOnline = false;
        this.logService.warn('Network connection lost');
      });
    });
  }

  private setupTimeChangeListeners(): void {
    if (!this.isBrowser) return;

    this.preciseMinuteInterval = setInterval(() => {
      const now = Date.now();
      const currentMinute = Math.floor(now / 60000);

      if (this.lastTimeCheck !== 0 && currentMinute !== this.lastTimeCheck) {
        this.logService.info('Minute boundary crossed, checking schedule');
        this.scheduleService.checkSchedule().subscribe({
          next: (changed) => {
            if (changed) {
              this.logService.info('Schedule changed during time check');
            }
          },
          error: (error) => {
            this.logService.error(`Error checking schedule: ${error}`);
          }
        });
      }

      this.lastTimeCheck = currentMinute;
    }, 1000);
  }

  private setupPlayback(): void {
    // Subscribe to current item changes from playback service
    const currentItemSub = this.playbackService.currentItem$.subscribe(item => {
      this.zone.run(() => {
        if (item) {
          this.addItemToCarousel(item);
        }
      });
    });

    // Subscribe to errors
    const errorSub = this.playbackService.playbackError$.subscribe(error => {
      this.zone.run(() => {
        this.playbackError = error;
        if (error) {
          this.logService.error(`Playback error: ${error}`);
        }
      });
    });

    // Subscribe to player state changes to detect new playlists
    const playerStateSub = this.playerState$.subscribe(state => {
      this.zone.run(() => {
        const previousPlaylistId = this.currentPlayerState?.currentPlaylistId;
        this.currentPlayerState = state;

        // If we have a new playlist, download all media and rebuild the carousel
        if (state.currentPlaylistId && state.currentPlaylistId !== previousPlaylistId) {
          this.logService.info(`New playlist detected: ${state.currentPlaylistId}, downloading all media`);
          this.downloadAllPlaylistMedia(state.currentPlaylistId);
        }
      });
    });

    this.subscriptions.push(currentItemSub, errorSub, playerStateSub);

    // Start playback
    this.playbackService.startPlayback();
  }

  private setupScheduleChecking(): void {
    // Check schedule every minute
    this.scheduleCheckInterval = this.scheduleService.scheduleChange$.subscribe(playlistId => {
      this.logService.info(`Schedule changed to playlist: ${playlistId}`);
      this.forceReloadPlaylist(playlistId);
    });
  }

  private setupPeriodicDeviceValidation(): void {
    // Validate device every 5 minutes
    setInterval(() => {
      this.validateDevice().then(isValid => {
        if (!isValid) {
          this.deviceValidationError = true;
          this.logService.error('Periodic device validation failed');
        }
      });
    }, 5 * 60 * 1000);
  }

  private startHeartbeat(): void {
    // Send periodic heartbeats
    this.heartbeatInterval = setInterval(() => {
      this.heartbeatService.sendHeartbeat({
        status: this.playbackError ? 'error' : this.isPlaying() ? 'playing' : 'paused',
        currentItem: this.currentItem?.playlistItem.id,
        currentPlaylist: this.currentPlayerState?.currentPlaylistId || null,
        error: this.playbackError,
        wakeLockActive: true, // Simplified for now
        wakeLockSupported: true,
        wakeLockMethods: 'screen'
      }).subscribe({
        next: (success) => {
          if (success) {
            this.logService.debug('Heartbeat sent successfully');
          } else {
            this.logService.warn('Heartbeat failed');
          }
        },
        error: (error) => {
          this.logService.error(`Heartbeat error: ${error}`);
        }
      });
    }, 30000); // Send heartbeat every 30 seconds
  }

  private isPlaying(): boolean {
    return this.currentPlayerState?.isPlaying || false;
  }

  // This method is now implemented below with enhanced functionality

  private forceReloadPlaylist(playlistId: string): void {
    this.logService.info(`Force reloading playlist: ${playlistId}`);

    if (this.playlistTransitionConfig.enabled) {
      this.performSmoothPlaylistTransition(playlistId);
    } else {
      this.performImmediatePlaylistTransition(playlistId);
    }
  }

  /**
   * Perform smooth playlist transition with crossfade or slide effect
   */
  private performSmoothPlaylistTransition(playlistId: string): void {
    this.logService.info(`Starting smooth transition to playlist: ${playlistId}`);

    // Start transition effect
    if (this.carouselContainer) {
      const container = this.carouselContainer.nativeElement;

      switch (this.playlistTransitionConfig.type) {
        case 'crossfade':
          container.style.transition = `opacity ${this.playlistTransitionConfig.duration}ms ease-in-out`;
          container.style.opacity = '0';
          break;

        case 'slide':
          container.style.transition = `transform ${this.playlistTransitionConfig.duration}ms ease-in-out`;
          container.style.transform = 'translateX(-100vw)';
          break;
      }
    }

    // Clear current state
    this.clearItemTimer();
    this.isTransitioning = true;

    // Wait for transition, then load new playlist
    setTimeout(() => {
      this.carouselItems = [];
      this.currentIndex = 0;

      // Reset carousel position
      if (this.carouselContainer) {
        const container = this.carouselContainer.nativeElement;
        container.style.transition = 'none';
        container.style.transform = 'translateX(0px)';
        container.style.opacity = '1';
      }

      // Load new playlist
      this.playbackService.reloadPlaylist();

      // End transition
      setTimeout(() => {
        this.isTransitioning = false;
        this.logService.info(`Smooth playlist transition completed`);
      }, 100);

    }, this.playlistTransitionConfig.duration);
  }

  /**
   * Perform immediate playlist transition
   */
  private performImmediatePlaylistTransition(playlistId: string): void {
    this.logService.info(`Starting immediate transition to playlist: ${playlistId}`);

    // Clear current carousel
    this.carouselItems = [];
    this.currentIndex = 0;
    this.clearItemTimer();

    // Reset carousel position
    if (this.carouselContainer) {
      const container = this.carouselContainer.nativeElement;
      container.style.transition = 'none';
      container.style.transform = 'translateX(0px)';
      container.style.opacity = '1';
    }

    // Reload playlist
    this.playbackService.reloadPlaylist();
  }

  /**
   * Download all media for a playlist and prepare carousel
   */
  private downloadAllPlaylistMedia(playlistId: string): void {
    // First try to get from cache, then from API
    this.contentSyncService.getCachedPlaylist(playlistId).subscribe({
      next: (cachedPlaylist) => {
        if (cachedPlaylist) {
          this.logService.info(`Using cached playlist for media download: ${cachedPlaylist.name}`);
          this.prepareCarouselFromPlaylist(cachedPlaylist);
        } else {
          // Get from API if not cached
          this.supabaseApi.getPlaylistById(playlistId).subscribe({
            next: (playlist) => {
              if (playlist && playlist.items && playlist.items.length > 0) {
                this.logService.info(`Downloaded playlist from API: ${playlist.name} (${playlist.items.length} items)`);
                this.prepareCarouselFromPlaylist(playlist);
              }
            },
            error: (error) => {
              this.logService.error(`Failed to get playlist from API: ${error.message}`);
            }
          });
        }
      },
      error: (error) => {
        this.logService.error(`Failed to get cached playlist: ${error.message}`);
      }
    });
  }

  /**
   * Prepare carousel from playlist and download all media
   */
  private prepareCarouselFromPlaylist(playlist: any): void {
    this.logService.info(`Preparing carousel for playlist: ${playlist.name} (${playlist.items.length} items)`);

    // Clear existing carousel items
    this.carouselItems = [];
    this.currentIndex = 0;
    this.clearItemTimer();

    // Create carousel items for all playlist items
    playlist.items.forEach((item: any) => {
      const carouselItem: CarouselItem = {
        id: item.id,
        playlistItem: item,
        localContentUrl: null,
        isLoaded: false,
        hasError: false,
        preloadStarted: false,
        loadStartTime: Date.now(),
        isContentReady: false,
        preloadElement: undefined
      };
      this.carouselItems.push(carouselItem);
    });

    // Download all media using ContentSyncService bulk download
    this.contentSyncService.downloadScheduledMedia([playlist]).subscribe({
      next: (status) => {
        this.logService.info(`Download progress: ${status.completedItems}/${status.totalItems} (${status.overallProgress.toFixed(1)}%)`);

        // Update carousel items as they complete
        this.updateCarouselItemsFromDownloadStatus(status);

        if (status.isComplete) {
          this.logService.info(`All media downloaded for playlist: ${playlist.name}`);
          // Start the carousel if not already started
          if (this.carouselItems.length > 0 && !this.itemTimer && !this.isTransitioning) {
            this.logService.info('Starting carousel timer after all media downloaded');
            this.startItemTimer();
          }
        }
      },
      error: (error) => {
        this.logService.error(`Failed to download playlist media: ${error.message}`);
        // Fallback to loading items individually
        this.loadCarouselItemsIndividually();
      }
    });
  }

  /**
   * Enhanced carousel item management with better loading
   */
  private addItemToCarousel(item: PlaylistItem): void {
    // Check if item already exists
    const existingIndex = this.carouselItems.findIndex(ci => ci.id === item.id);
    if (existingIndex !== -1) {
      return; // Item already exists
    }

    // Create carousel item
    const carouselItem: CarouselItem = {
      id: item.id,
      playlistItem: item,
      localContentUrl: null,
      isLoaded: false,
      hasError: false,
      preloadStarted: false,
      loadStartTime: Date.now(),
      isContentReady: false,
      preloadElement: undefined
    };

    // Add to carousel
    this.carouselItems.push(carouselItem);

    // Load content URL with priority for first few items
    const isHighPriority = this.carouselItems.length <= 3;
    this.loadCarouselItemContent(carouselItem, isHighPriority);

    // If this is the first item, start the carousel
    if (this.carouselItems.length === 1 && !this.itemTimer && !this.isTransitioning) {
      this.logService.info('Starting carousel timer for first item');
      this.startItemTimer();
    }

    this.logService.info(`Added item to carousel: ${item.name} (${this.carouselItems.length} total items)`);
  }

  /**
   * Update carousel items based on download status
   */
  private updateCarouselItemsFromDownloadStatus(status: BulkDownloadStatus): void {
    // For each carousel item, check if its content has been downloaded
    this.carouselItems.forEach(carouselItem => {
      if (!carouselItem.isLoaded && carouselItem.playlistItem.content?.url) {
        // Check if this URL is in the completed downloads
        this.contentSyncService.getLocalContentUrl(carouselItem.playlistItem.content.url).subscribe({
          next: (localUrl) => {
            if (localUrl && localUrl !== carouselItem.playlistItem.content.url) {
              // Content has been downloaded and cached
              carouselItem.localContentUrl = localUrl;
              carouselItem.isLoaded = true;
              carouselItem.hasError = false;
              this.logService.debug(`Updated carousel item with local URL: ${carouselItem.playlistItem.name}`);
            }
          },
          error: (error) => {
            this.logService.debug(`Content not yet available for: ${carouselItem.playlistItem.name}`);
          }
        });
      }
    });
  }

  /**
   * Fallback method to load carousel items individually
   */
  private loadCarouselItemsIndividually(): void {
    this.logService.info('Loading carousel items individually as fallback');

    this.carouselItems.forEach((carouselItem, index) => {
      const isHighPriority = index < 3; // First 3 items are high priority
      this.loadCarouselItemContent(carouselItem, isHighPriority);
    });

    // Start the carousel if not already started
    if (this.carouselItems.length > 0 && !this.itemTimer && !this.isTransitioning) {
      // Wait a bit for the first item to load, but not too long
      setTimeout(() => {
        if (!this.itemTimer && !this.isTransitioning) {
          this.logService.info('Starting carousel timer after individual item loading');
          this.startItemTimer();
        }
      }, 2000); // Reduced from 1000ms to 2000ms for better loading
    }
  }

  /**
   * Load content for a carousel item with priority handling
   */
  private loadCarouselItemContent(carouselItem: CarouselItem, isHighPriority: boolean = false): void {
    carouselItem.preloadStarted = true;

    const loadStartTime = Date.now();

    this.contentSyncService.getLocalContentUrl(carouselItem.playlistItem.content.url).subscribe({
      next: (localUrl) => {
        carouselItem.localContentUrl = localUrl;
        carouselItem.isLoaded = true;

        // Create preload element for better readiness detection
        this.createPreloadElement(carouselItem, isHighPriority);

        const loadTime = Date.now() - loadStartTime;
        this.logService.debug(`Loaded ${isHighPriority ? 'high-priority ' : ''}item: ${carouselItem.playlistItem.name} in ${loadTime}ms`);
      },
      error: (error) => {
        this.logService.error(`Error loading content for ${carouselItem.playlistItem.name}: ${error.message}`);
        carouselItem.localContentUrl = carouselItem.playlistItem.content.url; // Fallback to original URL
        carouselItem.hasError = true;
        carouselItem.isLoaded = true;
        carouselItem.isContentReady = true; // Mark as ready even with error to prevent blocking
      }
    });
  }

  // Fullscreen and wake lock methods
  private enterFullscreen(): void {
    if (!this.isBrowser) return;

    const element = this.elementRef.nativeElement;
    if (element.requestFullscreen) {
      element.requestFullscreen().catch((err: any) => {
        this.logService.warn(`Error attempting to enable fullscreen: ${err.message}`);
      });
    }
  }

  private requestWakeLock(): void {
    if (!this.isBrowser) return;
    this.wakeLockService.requestWakeLock();
  }

  private releaseWakeLock(): void {
    if (!this.isBrowser) return;
    this.wakeLockService.releaseWakeLock();
  }

  // Public methods for manual control
  skipToNext(): void {
    this.slideToNext();
  }

  restartPlayback(): void {
    this.playbackService.restartPlayback();
  }

  reloadPlaylist(): void {
    if (this.currentPlayerState?.currentPlaylistId) {
      this.forceReloadPlaylist(this.currentPlayerState.currentPlaylistId);
    } else {
      this.playbackService.reloadPlaylist();
    }
  }
}